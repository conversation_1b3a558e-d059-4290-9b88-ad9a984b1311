﻿namespace GMCadiomCore.Authentications.PermissionsAndSessions
{
    public class BaseSession
    {
        public static bool OpenFormAuthorization(string OpenFormName)
        {
            var screen = ScreensAccesses.SingleOrDefault(x => x.ScreenName == OpenFormName);

            if (screen != null)
            {
                return true;
            }
            else
            {
                throw new Exception("غير مصرح لك");
            }
        }

        public static bool CheckActionAuthorization(string formName, Actions actions, BaseUserModel? user = null)
        {
            if (user == null)
                user = User;

            if (user.UserType == UserTypeEnumeration.Admin.Value)
                return true;
            else
            {
                var screen = ScreensAccesses.SingleOrDefault(x => x.ScreenName == formName);
                bool flag = true;
                if (screen != null)
                {
                    switch (actions)
                    {
                        case Actions.Add:
                            flag = screen.CanAdd;
                            break;
                        case Actions.Edit:
                            flag = screen.CanEdit;
                            break;
                        case Actions.Delete:
                            flag = screen.CanDelete;
                            break;
                        case Actions.Print:
                            flag = screen.CanPrint;
                            break;
                        default:
                            break;
                    }
                }
                if (flag == false)
                {
                    throw new Exception("غير مصرح لك");
                }
                return flag;
            }
        }

        public static void SetUser(BaseUserModel user, List<BaseScreensAccessTemplate> getScreens, List<BaseScreensAccessProfileModel> getScreensProfile)
        {
            _user = user;

            BaseScreens.AddScreen(getScreens);

            if (user.UserType == UserTypeEnumeration.Admin.Value)
                _screensAccesses = BaseScreens.GetScreens;
            else
                _screensAccesses = (from gs in BaseScreens.GetScreens
                                    from db in getScreensProfile
                                    .Where(x => x.ScreenId == gs.ScreenId).DefaultIfEmpty()
                                    select new BaseScreensAccessTemplate(null, gs.ScreenName)
                                    {
                                        ScreenId = gs.ScreenId,
                                        HasChild = (db == null) ? false : gs.HasChild,
                                        ParentScreenId = gs.ParentScreenId,
                                        ScreenName = gs.ScreenName,
                                        ScreenText = gs.ScreenText,
                                        CanAdd = (db == null) ? true : db.CanAdd,
                                        CanDelete = (db == null) ? true : db.CanDelete,
                                        CanEdit = (db == null) ? true : db.CanEdit,
                                        CanOpen = (db == null) ? true : db.CanOpen,
                                        CanPrint = (db == null) ? true : db.CanPrint,
                                        CanShow = (db == null) ? true : db.CanShow,
                                        Actions = gs.Actions,
                                    }).ToList();
        }

        private static BaseUserModel _user;
        public static BaseUserModel User { get { return _user; } }

        private static List<BaseScreensAccessTemplate> _screensAccesses;
        public static List<BaseScreensAccessTemplate> ScreensAccesses { get { return _screensAccesses; } }
    }
}
