﻿namespace GMCadiomCore.Authentications.PermissionsAndSessions
{
    public enum Actions
    {
        Show = 1,
        Open,
        Add,
        Edit,
        Delete,
        Print,
    }

    [Table("ScreensAccessTemplate")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<BaseScreensAccessTemplate>))]
    public class BaseScreensAccessTemplate : BaseScreensAccessProfileModel
    {
        public static int MaxId = 1;

        //Used For TypeDescriptionProvider
        public BaseScreensAccessTemplate() { }
        public BaseScreensAccessTemplate(BaseScreensAccessTemplate? parent = null, [CallerMemberName] string? name = "")
        {
            if (name != null)
                ScreenName = name;
            ScreenId = MaxId++;
            if (parent != null)
                ParentScreenId = parent.ScreenId;
            else ParentScreenId = 0;
            CanShow = true;
            Actions = new List<Actions>()
            {
                PermissionsAndSessions.Actions.Add,
                PermissionsAndSessions.Actions.Edit,
                PermissionsAndSessions.Actions.Delete,
                PermissionsAndSessions.Actions.Print,
                PermissionsAndSessions.Actions.Show,
                PermissionsAndSessions.Actions.Open,
            };
        }
        [Browsable(false)]
        public bool HasChild { get; set; }
        [Browsable(false)]
        public int ParentScreenId { get; set; }
        [Browsable(false)]
        public string ScreenName { get; set; }
        [DisplayName("اسم الشاشه")]
        public string ScreenText { get; set; }
        public List<Actions> Actions { get; set; }
    }
}
